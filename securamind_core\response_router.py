"""
Response Router

This module handles routing responses from worker processes to the correct client
channels, ensuring perfect isolation and no cross-contamination.
"""

import asyncio
import logging
import multiprocessing as mp
import queue
from typing import Dict

logger = logging.getLogger(__name__)


class ResponseRouter:
    """Routes responses from shared queue to individual client channels"""
    
    def __init__(self, process_name: str):
        self.process_name = process_name
        self.response_channels: Dict[str, asyncio.Queue] = {}
        self.channels_lock = asyncio.Lock()
        self.shared_response_queue: mp.Queue = None
        self.running = False
        self.router_task: asyncio.Task = None
    
    def start(self, shared_response_queue: mp.Queue):
        """Start the response router"""
        self.shared_response_queue = shared_response_queue
        self.running = True
        self.router_task = asyncio.create_task(self._route_responses())
    
    async def stop(self):
        """Stop the response router"""
        self.running = False
        if self.router_task:
            self.router_task.cancel()
            try:
                await self.router_task
            except asyncio.CancelledError:
                pass
        
        # Clean up all channels
        async with self.channels_lock:
            self.response_channels.clear()
    
    async def create_channel(self, request_id: str) -> asyncio.Queue:
        """Create a new response channel for a request"""
        channel = asyncio.Queue()
        async with self.channels_lock:
            self.response_channels[request_id] = channel
        return channel
    
    async def remove_channel(self, request_id: str):
        """Remove a response channel"""
        async with self.channels_lock:
            self.response_channels.pop(request_id, None)
    
    def get_active_channel_count(self) -> int:
        """Get the number of active channels"""
        return len(self.response_channels)
    
    async def _route_responses(self):
        """Main response routing loop"""
        while self.running:
            try:
                if not self.shared_response_queue:
                    await asyncio.sleep(0.1)
                    continue
                
                # Get response from shared queue (non-blocking with short timeout)
                try:
                    response = await asyncio.to_thread(
                        self.shared_response_queue.get, 
                        timeout=0.1
                    )
                except queue.Empty:
                    continue
                
                # Route response to correct channel
                await self._route_single_response(response)
                
            except Exception as e:
                logger.error(f"Error in response router for {self.process_name}: {e}", exc_info=True)
                await asyncio.sleep(1)
    
    async def _route_single_response(self, response):
        """Route a single response to the correct channel"""
        if not isinstance(response, dict) or "request_id" not in response:
            logger.warning(f"Received response without request_id: {response}")
            return
        
        request_id = response["request_id"]
        
        async with self.channels_lock:
            if request_id in self.response_channels:
                # Remove request_id from response before sending to channel
                clean_response = {k: v for k, v in response.items() if k != "request_id"}
                await self.response_channels[request_id].put(clean_response)
            else:
                # This is expected when clients disconnect - their channels are cleaned up
                logger.debug(f"Received response for unknown request_id: {request_id}")
