#!/usr/bin/env python3
"""
Simple concurrent processing verification test for SecuraMind.
This test focuses on verifying that exactly 2 requests can process simultaneously.
"""

import asyncio
import aiohttp
import time
import logging
from datetime import datetime

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s.%(msecs)03d - %(levelname)s - %(message)s',
    datefmt='%H:%M:%S'
)
logger = logging.getLogger(__name__)

async def send_inference_request(session, user_id):
    """Send a single inference request and track token timing"""
    url = "http://127.0.0.1:8000/api/v1/inference"
    
    data = {
        "query": f"Please count from 1 to 20 slowly, saying 'User {user_id} count X' for each number.",
        "collection_name": "concurrent_test",
        "config": {
            "max_new_tokens": 200,
            "temperature": 0.1
        },
        "language": "en"
    }
    
    start_time = time.time()
    token_times = []
    
    try:
        logger.info(f"🚀 User {user_id}: Starting request at {datetime.now().strftime('%H:%M:%S.%f')[:-3]}")
        
        async with session.post(url, json=data) as response:
            if response.status != 200:
                logger.error(f"❌ User {user_id}: HTTP {response.status}")
                return None
                
            logger.info(f"✅ User {user_id}: Connected, waiting for tokens...")
            
            async for line in response.content:
                if line:
                    line_str = line.decode('utf-8').strip()
                    if line_str.startswith('data: '):
                        data_content = line_str[6:]  # Remove 'data: ' prefix
                        if data_content and data_content != '[DONE]':
                            current_time = time.time()
                            elapsed = current_time - start_time
                            token_times.append(elapsed)
                            
                            # Log every 5th token to reduce noise
                            if len(token_times) % 5 == 0:
                                logger.info(f"🎯 User {user_id}: Token {len(token_times)} at {elapsed:.2f}s")
                        
                        elif data_content == '[DONE]':
                            total_time = time.time() - start_time
                            logger.info(f"🏁 User {user_id}: Completed in {total_time:.2f}s with {len(token_times)} tokens")
                            break
                            
    except Exception as e:
        logger.error(f"❌ User {user_id}: Error - {e}")
        return None
    
    return {
        'user_id': user_id,
        'token_count': len(token_times),
        'total_time': time.time() - start_time,
        'first_token_time': token_times[0] if token_times else None,
        'token_times': token_times
    }

async def test_concurrent_processing():
    """Test that exactly 2 requests can process concurrently"""
    logger.info("🧪 CONCURRENT PROCESSING VERIFICATION TEST")
    logger.info("=" * 60)
    
    # Setup test collection
    async with aiohttp.ClientSession() as session:
        # Create test collection
        setup_url = "http://127.0.0.1:8000/api/v1/ingest/text"
        setup_data = {
            "source_identifier": "concurrent_verification_test",
            "source_name": "Concurrent Verification Test Collection",
            "collection_name": "concurrent_test",
            "text_content": "This is test data for concurrent processing verification. It contains information about testing concurrent request handling in SecuraMind."
        }
        
        async with session.post(setup_url, json=setup_data) as response:
            if response.status == 200:
                logger.info("✅ Test collection setup successful")
            else:
                logger.error(f"❌ Test collection setup failed: {response.status}")
                return
        
        # Wait a moment for ingestion
        await asyncio.sleep(2)
        
        # Launch exactly 4 requests simultaneously
        logger.info("🚀 Launching 4 simultaneous requests...")
        logger.info("Expected: First 2 should start immediately, next 2 should queue")
        
        start_time = time.time()
        tasks = []
        for i in range(1, 5):
            task = asyncio.create_task(send_inference_request(session, i))
            tasks.append(task)
        
        # Wait for all requests to complete
        results = await asyncio.gather(*tasks, return_exceptions=True)
        
        # Analyze results
        logger.info("\n📊 ANALYSIS")
        logger.info("=" * 60)
        
        valid_results = [r for r in results if r and not isinstance(r, Exception)]
        
        if len(valid_results) >= 2:
            # Sort by first token time
            valid_results.sort(key=lambda x: x['first_token_time'] or float('inf'))
            
            first_two = valid_results[:2]
            logger.info(f"First 2 requests:")
            for result in first_two:
                logger.info(f"  User {result['user_id']}: First token at {result['first_token_time']:.2f}s")
            
            # Check if first 2 started within reasonable time of each other
            if len(first_two) == 2:
                time_diff = abs(first_two[1]['first_token_time'] - first_two[0]['first_token_time'])
                logger.info(f"Time difference between first 2: {time_diff:.2f}s")
                
                if time_diff < 5.0:  # Allow 5 second tolerance
                    logger.info("✅ CONCURRENT PROCESSING DETECTED!")
                    logger.info("   First 2 requests started within 5 seconds of each other")
                else:
                    logger.warning("⚠️  Sequential processing detected")
                    logger.warning(f"   {time_diff:.2f}s gap between first 2 requests")
            
            # Check token interleaving in first 30 seconds
            logger.info("\n🔍 Checking for token interleaving...")
            interleaving_detected = False
            
            if len(first_two) == 2:
                user1_tokens = first_two[0]['token_times'][:10]  # First 10 tokens
                user2_tokens = first_two[1]['token_times'][:10]  # First 10 tokens
                
                # Check if tokens from both users appear in overlapping time windows
                for i, t1 in enumerate(user1_tokens):
                    for j, t2 in enumerate(user2_tokens):
                        if abs(t1 - t2) < 2.0:  # Within 2 seconds
                            logger.info(f"✅ Interleaving detected: User 1 token {i+1} at {t1:.2f}s, User 2 token {j+1} at {t2:.2f}s")
                            interleaving_detected = True
                            break
                    if interleaving_detected:
                        break
            
            if not interleaving_detected:
                logger.warning("⚠️  No clear token interleaving detected")
        
        else:
            logger.error("❌ Insufficient valid results for analysis")
        
        logger.info("\n🎯 TEST COMPLETE")

if __name__ == "__main__":
    asyncio.run(test_concurrent_processing())
