# Mindend

This is the Mindend – the AI-powered core of our system.

It connects the backend logic with the LLM and handles inference and embeddings.

## Purpose

The Mindend (AI Backend) handles:
- Loading and managing local Large Language Models (LLMs).
- Providing an API for LLM inference (text generation).
- (Future) Generating vector embeddings from various data sources.
- (Future) Performing Retrieval Augmented Generation (RAG) using embedded data.
- Some data pre- and post-processing for inference.

## Status

🚧 Work in progress – this module is under active development. Phase 3 (RAG) is functional.

## Tech Stack

- **Python 3.9+**
- **FastAPI**: For creating the REST API.
- **Pydantic**: For data validation and settings management.
- **llama-cpp-python**: For running GGUF-quantized LLMs locally (e.g., Gemma).
- **Uvicorn**: ASGI server for FastAPI.
- **(Future)** Qdrant (Vector Database), Sentence-Transformers/LangChain (Embeddings, Text Processing).

## Quick Start & Deployment

Follow these steps to get the Mindend up and running locally.

### Prerequisites

1.  **Python 3.9+**: Ensure you have a compatible Python version installed.
2.  **Git**: For cloning the repository.

### Setup

1.  **Clone the Repository (if you haven't already):**
    ```bash
    git clone https://gitlab.com/securamind/securamind-mindend
    cd securamind-mindend
    ```

2.  **Create and Activate a Virtual Environment:**
    ```bash
    python -m venv venv
    # On Windows:
    venv\Scripts\activate
    # On macOS/Linux:
    source venv/bin/activate
    ```

3.  **Install Dependencies:**
    ```bash
    pip install -r requirements.txt
    ```

4.  **Download LLM & Embedding Model:**
    *   Download a GGUF-quantized model: https://huggingface.co/tensorblock/gemma-3-4b-it-GGUF?show_file_info=gemma-3-4b-it-Q4_K_M.gguf
    *   Download a GGUF embedding model: https://huggingface.co/alela32/USER-bge-m3-Q8_0-GGUF?show_file_info=user-bge-m3-q8_0.gguf
    *   Place the model files into the `models/` directory within the project root. Create the `models` directory if it doesn't exist.
        *   Example: `models/gemma-3-4b-it-Q4_K_M.gguf` & `models/user-bge-m3-q8_0.gguf.gguf` 

5.  **Optional - Configure Environment Variables:**
    *   If you need a custom setup, create a new `.env` file in the project root.
    *   Create variables which you need to be custom, especially `MODEL_FILENAME` if you downloaded a different model or named it differently.
    *   You can use the .env.example to check which variables are available.

### Running the Application

Start the application with:

```bash
python -m securamind_core.main
```

Start the FastAPI application with custom settings using Uvicorn:

```bash
uvicorn securamind_core.main:app --reload --host 127.0.0.1 --port 8000
```

The API will be available at `http://127.0.0.1:8000` (or the host/port you configured).

## API Usage & Testing

### API Documentation

Interactive API documentation (Swagger UI and ReDoc) is automatically generated by FastAPI:
-   **Swagger UI**: [http://127.0.0.1:8000/docs](http://127.0.0.1:8000/docs)
-   **ReDoc**: [http://127.0.0.1:8000/redoc](http://127.0.0.1:8000/redoc)

Use these interfaces to explore endpoints, schemas, and try out API calls directly from your browser.

### Testing with `curl`

You can test the API endpoints using `curl`.

**1. Ingesting Data (Example with Text):**

This command ingests a simple text string into a collection named "test".

```bash
curl -X POST -H "Content-Type: application/json" -d "{\"collection_name\":\"test\", \"text_content\":\"The secret codename is: apple\"}" http://127.0.0.1:8000/api/v1/ingest/text
```

Expected Response:
```json
{"collection_name":"test","documents_processed":1,"total_chunks_embedded":1,"status":"success"}
```

**2. Performing RAG Inference:**

This command queries the LLM using the data previously ingested into the "test" collection.

```bash
curl -N -X POST -H "Content-Type: application/json" -d "{\"query\":\"What was the secret codename again?\", \"collection_name\":\"test\", \"config\":{\"max_new_tokens\":500}}" http://127.0.0.1:8000/api/v1/inference
```
*   `-N`: Disables buffering for streaming.

Expected Streamed Response:
The Mindend will stream back tokens. Before the end-of-stream (EOS) event, it will also send an event containing the sources used for the response.
```
data: {"token": "The"}

data: {"token": " secret"}

data: {"token": " cod"}

data: {"token": "ename"}

data: {"token": " is"}

data: {"token": ":"}

data: {"token": " apple"}

data: {"token": "."}

data: {"event": "sources", "data": [{"source": "direct_text_input", "page": null, "text_preview": "The secret codename is: apple..."}]}

data: {"event": "eos", "message": "End of stream"}
```

**Note on `collection_name`:** For the `/api/v1/inference` endpoint, the `collection_name` field in the JSON request body is now crucial as it tells the system which set of embedded data to use for Retrieval Augmented Generation.

### Testing with `springboot`

```java
// Assuming 'webClient' is an injected and configured WebClient instance
// And 'apiRequestJson' is a String containing your JSON request payload.
// Example request payload for RAG:
// String apiRequestJson = "{\"query\":\"What is the secret?\",\"collection_name\":\"test\",\"config\":{\"max_new_tokens\":50}}";
// Can also contain chatHistory: \"chat_history\":[{\"role\":\"user\",\"content\":\"Previous question\"},{\"role\":\"assistant\",\"content\":\"Previous answer\"}]
// And an optional system_prompt: \"system_prompt\":\"Tell the user that the secret codename is banana.\"

Flux<String> sseEventStream = webClient.post()
    .uri("/api/v1/inference") // Or full URL if webClient base URL isn't set
    .contentType(MediaType.APPLICATION_JSON)
    .accept(MediaType.TEXT_EVENT_STREAM)
    .bodyValue(apiRequestJson) // Or .body(BodyInserters.fromValue(yourRequestDto))
    .retrieve()
    .bodyToFlux(String.class); // Gets raw SSE lines: "data: {...}"

// To consume (example):
sseEventStream.subscribe(eventLine -> {
    // Process each eventLine here.
    System.out.println("Raw SSE Line: " + eventLine);
});
```

## Future Development (Roadmap)

-   **Phase 4**: Enhancements (multiple model support, config management) & Packaging.
-   **Phase 5**: Finalization (security, advanced docs, testing).