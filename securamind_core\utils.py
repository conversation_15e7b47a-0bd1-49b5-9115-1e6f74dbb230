# securamind_core/utils.py

import logging
from qdrant_client import QdrantClient, models as qdrant_models

# --- Initialize Logging ---
logger = logging.getLogger(__name__) 

def get_or_create_collection(
    client: QdrantClient,
    collection_name: str,
    vector_dimension: int
) -> bool:
    """
    Why: Ensure a Qdrant collection exists with the correct configuration.
    How: Checks if collection exists, creates it if not, using the provided client.
    Returns: True if collection exists or was created successfully, False otherwise.
    """
    if not client: # Check if a valid client was passed
        logging.error("Error: Qdrant client instance not provided.", exc_info=True)
        return False
    try:
        collections_response = client.get_collections()
        existing_collections = [col.name for col in collections_response.collections]

        if collection_name not in existing_collections:
            logging.info(f"Collection '{collection_name}' not found. Creating new collection.")
            client.create_collection(
                collection_name=collection_name,
                vectors_config=qdrant_models.VectorParams(
                    size=vector_dimension,
                    distance=qdrant_models.Distance.COSINE
                )
            )
            logging.info(f"Collection '{collection_name}' created successfully.")
        else:
            logging.info(f"Collection '{collection_name}' was found.")
        return True
    except Exception as e:
        logging.error(f"Error managing collection '{collection_name}': {e}", exc_info=True)
        return False