#!/usr/bin/env python3
"""
Test Runner for Comprehensive Concurrent Processing Test

This script runs the comprehensive concurrent processing test and provides
a simple interface for validating SecuraMind's concurrent capabilities.

Usage:
    python test/run_concurrent_test.py

The test will:
1. Check if SecuraMind server is running
2. Execute the comprehensive concurrent processing test
3. Provide detailed results and validation
4. Generate a test report with timestamps and metrics
"""

import asyncio
import sys
import os
import subprocess
import time
import aiohttp
from pathlib import Path

# Add the project root to Python path
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

async def check_server_health(base_url: str = "http://127.0.0.1:8000") -> bool:
    """Check if SecuraMind server is running and healthy"""
    try:
        timeout = aiohttp.ClientTimeout(total=10)
        async with aiohttp.ClientSession(timeout=timeout) as session:
            # Try to access the health endpoint
            async with session.get(f"{base_url}/api/v1/health") as response:
                if response.status == 200:
                    health_data = await response.json()
                    print("✅ SecuraMind server is running and accessible")
                    print(f"   Main LLM: {health_data.get('main_llm_status', 'unknown')}")
                    print(f"   Backup LLM: {health_data.get('backup_llm_status', 'unknown')}")
                    print(f"   Embedding LLM: {health_data.get('embedding_llm_status', 'unknown')}")
                    print(f"   Concurrent capacity: {health_data.get('concurrent_capacity', 0)}")
                    return True
                else:
                    print(f"❌ Health endpoint responded with status {response.status}")
                    return False
    except Exception as e:
        print(f"❌ Cannot connect to SecuraMind server: {e}")
        print(f"   Make sure the server is running at {base_url}")
        return False

def print_test_header():
    """Print test header with information"""
    print("=" * 80)
    print("🧪 SECURAMIND COMPREHENSIVE CONCURRENT PROCESSING TEST")
    print("=" * 80)
    print()
    print("This test validates:")
    print("• Worker pool architecture and LLM instance scheduling")
    print("• Concurrent request handling (30 simultaneous requests)")
    print("• Request queueing and dynamic worker assignment")
    print("• Streaming isolation and response routing")
    print("• Fault tolerance and system stability")
    print()
    print("Expected behavior:")
    print("• First 2 inference requests process concurrently")
    print("• Remaining 18 inference requests queue properly")
    print("• All 10 ingestion requests process with unique IDs")
    print("• Zero server crashes throughout the test")
    print("• All 30 requests complete successfully")
    print()
    print("=" * 80)
    print()

async def run_test():
    """Run the comprehensive concurrent processing test"""
    print_test_header()
    
    # Check server health
    print("🔍 Checking SecuraMind server status...")
    if not await check_server_health():
        print("\n❌ Cannot proceed with test - server is not accessible")
        print("\nTo start the SecuraMind server:")
        print("   python -m securamind_core.main")
        return False
    
    print("\n🚀 Starting comprehensive concurrent processing test...")
    print("   This may take several minutes to complete...")
    print()
    
    # Import and run the test
    try:
        from test_comprehensive_concurrent_processing import ConcurrentProcessingTester
        
        async with ConcurrentProcessingTester() as tester:
            metrics = await tester.run_comprehensive_test()
            
            # Print final results
            print("\n" + "=" * 80)
            print("📊 FINAL TEST RESULTS")
            print("=" * 80)
            
            total_time = metrics.test_end_time - metrics.test_start_time
            print(f"Test Duration: {total_time:.2f} seconds")
            print(f"Total Requests: {metrics.total_requests}")
            print(f"Successful: {metrics.successful_requests}")
            print(f"Failed: {metrics.failed_requests}")
            print(f"Server Crashes: {metrics.server_crashes}")
            print(f"Ingestion IDs: {len(metrics.ingestion_ids)}")
            
            # Determine overall success
            success_criteria = [
                (metrics.server_crashes == 0, "Zero server crashes"),
                (metrics.successful_requests == 30, "All 30 requests successful"),
                (metrics.failed_requests == 0, "No failed requests"),
                (len(metrics.ingestion_ids) == 10, "All 10 ingestion IDs generated")
            ]
            
            print("\n🎯 Success Criteria:")
            all_passed = True
            for passed, description in success_criteria:
                status = "✅" if passed else "❌"
                print(f"   {status} {description}")
                if not passed:
                    all_passed = False
            
            print("\n" + "=" * 80)
            if all_passed:
                print("🎉 COMPREHENSIVE TEST PASSED!")
                print("   SecuraMind concurrent processing is working correctly")
            else:
                print("❌ TEST FAILED - Some criteria not met")
                print("   Check the detailed logs in test/concurrent_test_log.txt")
            print("=" * 80)
            
            return all_passed
            
    except ImportError as e:
        print(f"❌ Cannot import test module: {e}")
        return False
    except Exception as e:
        print(f"❌ Test execution failed: {e}")
        return False

def main():
    """Main entry point"""
    try:
        success = asyncio.run(run_test())
        return 0 if success else 1
    except KeyboardInterrupt:
        print("\n\n⚠️ Test interrupted by user")
        return 1
    except Exception as e:
        print(f"\n❌ Unexpected error: {e}")
        return 1

if __name__ == "__main__":
    exit_code = main()
    sys.exit(exit_code)
