"""
LLM Process Manager (Legacy Compatibility)

This module provides backward compatibility by re-exporting the refactored
LLM management components. The actual implementation has been split into
focused modules for better maintainability.

Refactored Architecture:
- llm_worker.py: Worker process functions
- llm_process.py: Individual LLM process management
- llm_manager.py: Process manager with health monitoring
- response_router.py: Response routing for stream isolation
"""

# Re-export all components for backward compatibility
from .llm_process import LLMProcess, LLMProcessConfig, LLMProcessStatus
from .llm_manager import LLMProcessManager, llm_process_manager
from .llm_worker import llm_worker_process
from .response_router import ResponseRouter

# Legacy alias for the worker process function
llm_worker_process_with_direct_routing = llm_worker_process

__all__ = [
    'LLMProcess',
    'LLMProcessConfig',
    'LLMProcessStatus',
    'LLMProcessManager',
    'llm_process_manager',
    'llm_worker_process',
    'llm_worker_process_with_direct_routing',
    'ResponseRouter'
]