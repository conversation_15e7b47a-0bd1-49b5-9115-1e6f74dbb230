"""
LLM Process Management

This module manages individual LLM processes with direct response routing
for perfect stream isolation and security.
"""

import asyncio
import logging
import multiprocessing as mp
import queue
import time
import uuid
from typing import Dict, Any, Optional, AsyncGenerator
from enum import Enum
from dataclasses import dataclass

from .llm_worker import llm_worker_process
from .response_router import ResponseRouter

logger = logging.getLogger(__name__)


class LLMProcessStatus(Enum):
    """Status of an LLM process"""
    STARTING = "starting"
    HEALTHY = "healthy"
    CRASHED = "crashed"
    RESTARTING = "restarting"
    STOPPED = "stopped"


@dataclass
class LLMProcessConfig:
    """Configuration for an LLM process"""
    name: str
    model_path: str
    n_ctx: int
    n_gpu_layers: int
    verbose: bool
    process_type: str  # "main", "backup", "embedding"


class LLMProcess:
    """Manages a single LLM process with direct response routing"""

    def __init__(self, config: LLMProcessConfig):
        self.config = config
        self.status = LLMProcessStatus.STOPPED
        self.process: Optional[mp.Process] = None
        self.request_queue: Optional[mp.Queue] = None
        self.health_queue: Optional[mp.Queue] = None
        self.last_health_check = 0
        self.restart_count = 0
        self.max_restarts = 5
        self.restart_delay = 2.0
        
        # Response routing
        self.response_router = ResponseRouter(config.name)
        self.shared_response_queue: Optional[mp.Queue] = None

    def start(self):
        """Start the LLM process with direct response routing"""
        if self.status != LLMProcessStatus.STOPPED:
            logger.warning(f"LLM process {self.config.name} is already running")
            return

        logger.info(f"Starting LLM process: {self.config.name}")
        self.status = LLMProcessStatus.STARTING

        # Create communication queues
        self.request_queue = mp.Queue()
        self.shared_response_queue = mp.Queue()
        # Unlimited health queue to prevent blocking under sustained load
        self.health_queue = mp.Queue()

        # Start response router
        self.response_router.start(self.shared_response_queue)

        # Start the worker process
        self.process = mp.Process(
            target=llm_worker_process,
            args=(
                self.config,
                self.request_queue,
                self.shared_response_queue,
                self.health_queue
            ),
            name=f"LLM-{self.config.name}"
        )
        self.process.start()

        # Wait for startup confirmation
        self._wait_for_startup()

    def _wait_for_startup(self):
        """Wait for the worker process to signal it's ready"""
        try:
            health_signal = self.health_queue.get(timeout=30)
            if health_signal == "ready":
                self.status = LLMProcessStatus.HEALTHY
                self.last_health_check = time.time()
                logger.info(f"LLM process {self.config.name} started successfully")
            else:
                raise Exception(f"Unexpected health signal: {health_signal}")
        except queue.Empty:
            logger.error(f"LLM process {self.config.name} failed to start (timeout)")
            self.stop()
            raise Exception(f"LLM process {self.config.name} startup timeout")

    async def stop(self):
        """Stop the LLM process and clean up resources"""
        if self.process and self.process.is_alive():
            logger.info(f"Stopping LLM process: {self.config.name}")
            self.process.terminate()
            self.process.join(timeout=10)
            if self.process.is_alive():
                logger.warning(f"Force killing LLM process: {self.config.name}")
                self.process.kill()
                self.process.join()

        # Clean up resources
        self.status = LLMProcessStatus.STOPPED
        self.process = None
        await self.response_router.stop()
        self.shared_response_queue = None

    def is_healthy(self) -> bool:
        """Check if the process is healthy"""
        if not self.process or not self.process.is_alive():
            return False

        # Check for recent health signals
        self._check_health_signals()

        # Consider healthy if we got a signal in the last 60 seconds
        # OR if the process is alive and we're within the first 60 seconds of startup
        # This prevents false negatives during sustained load when health signals may be delayed
        time_since_last_health = time.time() - self.last_health_check

        # If we have recent health signals, definitely healthy
        if time_since_last_health < 60:
            return True

        # Fallback: if process is alive and we haven't exceeded max health timeout, still consider healthy
        # This prevents false negatives when health queue gets backed up under load
        return time_since_last_health < 120 # TODO: Double check if timeout is needed

    def _check_health_signals(self):
        """Check for health signals from worker process"""
        try:
            while not self.health_queue.empty():
                health_signal = self.health_queue.get_nowait()
                if health_signal == "alive":
                    self.last_health_check = time.time()
        except queue.Empty:
            pass

    def is_available(self) -> bool:
        """Check if the process is available for requests (healthy AND not busy)"""
        if not self.is_healthy():
            return False

        # Check if the process is currently busy processing requests
        active_requests = self.get_active_request_count()

        # Allow exactly 1 concurrent request per LLM instance to maintain thread safety
        # while enabling true concurrent processing across the 2-LLM system
        # This prevents GGML_ASSERT failures while allowing 2 total concurrent requests
        return active_requests == 0

    def get_active_request_count(self) -> int:
        """Get the number of currently active requests"""
        return self.response_router.get_active_channel_count()

    async def restart(self) -> bool:
        """Restart the LLM process"""
        if self.restart_count >= self.max_restarts:
            logger.error(f"LLM process {self.config.name} exceeded max restarts ({self.max_restarts})")
            self.status = LLMProcessStatus.CRASHED
            return False

        logger.info(f"Restarting LLM process: {self.config.name} (attempt {self.restart_count + 1})")
        self.status = LLMProcessStatus.RESTARTING

        await self.stop()
        time.sleep(self.restart_delay)

        try:
            self.start()
            self.restart_count += 1
            return True
        except Exception as e:
            logger.error(f"Failed to restart LLM process {self.config.name}: {e}")
            self.status = LLMProcessStatus.CRASHED
            return False

    async def send_request(self, request: Dict[str, Any]) -> Any:
        """Send a request to the LLM process using direct response routing"""
        if not self.is_healthy():
            raise Exception(f"LLM process {self.config.name} is not healthy")

        # Generate unique request ID for routing
        request_id = str(uuid.uuid4())
        request["request_id"] = request_id

        # Create dedicated response channel for this request
        response_channel = await self.response_router.create_channel(request_id)

        try:
            # Send request to worker process
            self.request_queue.put(request, block=True)

            if request.get("action") == "embedding":
                # For embeddings, wait for single response
                return await self._get_embedding_response(request_id, response_channel)
            else:
                # For streaming chat completions, return generator
                return self._stream_response_generator(request_id, response_channel)

        except Exception as e:
            # Clean up on error
            await self.response_router.remove_channel(request_id)
            raise e

    async def _get_embedding_response(self, request_id: str, response_channel: asyncio.Queue) -> Dict[str, Any]:
        """Get embedding response from dedicated channel"""
        try:
            # Wait for response (no timeout - direct channel ensures completion)
            response = await response_channel.get()

            if isinstance(response, dict) and "error" in response:
                raise Exception(f"LLM process error: {response['error']}")

            return response

        finally:
            # Always clean up
            await self.response_router.remove_channel(request_id)

    async def _stream_response_generator(self, request_id: str, response_channel: asyncio.Queue) -> AsyncGenerator[Dict[str, Any], None]:
        """Generator that yields streaming tokens from dedicated channel"""
        try:
            # Wait for stream start signal
            start_response = await response_channel.get()

            if isinstance(start_response, dict) and "error" in start_response:
                raise Exception(f"LLM process error: {start_response['error']}")

            if not (isinstance(start_response, dict) and start_response.get("stream_start")):
                raise Exception(f"Expected stream_start signal, got: {start_response}")

            # Yield tokens until stream ends
            while True:
                token_response = await response_channel.get()

                if isinstance(token_response, dict):
                    if "error" in token_response:
                        raise Exception(f"LLM process error: {token_response['error']}")
                    elif "stream_end" in token_response:
                        break
                    elif "token" in token_response:
                        # Yield token in the format expected by the streaming system
                        yield {
                            "choices": [{
                                "delta": {
                                    "content": token_response["token"]
                                }
                            }]
                        }

        except asyncio.CancelledError:
            # Client disconnected - just clean up, no draining needed!
            logger.info(f"Client disconnected from {self.config.name} request {request_id}")
            raise
        finally:
            # Always clean up this request's resources
            await self.response_router.remove_channel(request_id)
