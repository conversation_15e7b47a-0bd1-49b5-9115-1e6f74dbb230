"""
LLM Process Manager

This module manages multiple LLM processes (main, backup, embedding) with
health monitoring and automatic restart capabilities.
"""

import asyncio
import logging
from typing import Dict, List, Optional, Any

from .config import settings
from .llm_process import LLMProcess, LLMProcessConfig

logger = logging.getLogger(__name__)


class LLMProcessManager:
    """Manages multiple LLM processes with health monitoring"""
    
    def __init__(self):
        self.processes: Dict[str, LLMProcess] = {}
        self.health_monitor_task: Optional[asyncio.Task] = None
        self.running = False
        
    async def start(self):
        """Start all LLM processes"""
        logger.info("Starting LLM Process Manager")
        self.running = True
        
        # Create and start all configured processes
        configs = self._create_process_configs()
        
        for config in configs:
            try:
                process = LLMProcess(config)
                process.start()
                self.processes[config.name] = process
                logger.info(f"Started LLM process: {config.name}")
            except Exception as e:
                logger.error(f"Failed to start LLM process {config.name}: {e}")
        
        # Start health monitoring
        self.health_monitor_task = asyncio.create_task(self._health_monitor())
        
    async def stop(self):
        """Stop all LLM processes"""
        logger.info("Stopping LLM Process Manager")
        self.running = False
        
        # Stop health monitoring
        if self.health_monitor_task:
            self.health_monitor_task.cancel()
            try:
                await self.health_monitor_task
            except asyncio.CancelledError:
                pass
        
        # Stop all processes
        for process in self.processes.values():
            await process.stop()
        
        self.processes.clear()
        
    def _create_process_configs(self) -> List[LLMProcessConfig]:
        """Create configurations for all LLM processes based on settings"""
        configs = []
        
        # Main LLM configuration
        main_model_path = settings.absolute_models_dir / settings.model_filename
        if main_model_path.exists():
            configs.append(LLMProcessConfig(
                name="main",
                model_path=str(main_model_path),
                n_ctx=settings.n_ctx,
                n_gpu_layers=settings.n_gpu_layers,
                verbose=settings.verbose_llm,
                process_type="main"
            ))
        else:
            logger.warning(f"Main model not found: {main_model_path}")
        
        # Backup LLM configuration
        if settings.backup_llm_enabled:
            backup_model_path = settings.absolute_models_dir / settings.backup_model_filename
            if backup_model_path.exists():
                configs.append(LLMProcessConfig(
                    name="backup",
                    model_path=str(backup_model_path),
                    n_ctx=settings.backup_n_ctx,
                    n_gpu_layers=settings.backup_n_gpu_layers,
                    verbose=settings.backup_verbose_llm,
                    process_type="backup"
                ))
            else:
                logger.warning(f"Backup model not found: {backup_model_path}")
        
        # Embedding LLM configuration
        embedding_model_path = settings.absolute_models_dir / settings.embedding_model_filename
        if embedding_model_path.exists():
            configs.append(LLMProcessConfig(
                name="embedding",
                model_path=str(embedding_model_path),
                n_ctx=2048,  # Embeddings don't need large context
                n_gpu_layers=settings.embedding_model_n_gpu_layers,
                verbose=settings.embedding_model_verbose,
                process_type="embedding"
            ))
        else:
            logger.warning(f"Embedding model not found: {embedding_model_path}")
        
        if not configs:
            raise Exception("No valid model configurations found")
            
        return configs
    
    async def _health_monitor(self):
        """Monitor health of all processes and restart if needed"""
        while self.running:
            try:
                for name, process in list(self.processes.items()):
                    if not process.is_healthy():
                        logger.warning(f"LLM process {name} is unhealthy, attempting restart")
                        restart_success = await process.restart()
                        if not restart_success:
                            logger.error(f"Failed to restart LLM process {name}")
                
                await asyncio.sleep(10)  # Check every 10 seconds
                
            except asyncio.CancelledError:
                break
            except Exception as e:
                logger.error(f"Error in health monitor: {e}", exc_info=True)
                await asyncio.sleep(5)
    
    def get_process(self, name: str) -> Optional[LLMProcess]:
        """Get a specific LLM process by name"""
        return self.processes.get(name)
    
    def get_process_status(self, name: str) -> str:
        """Get the status of a specific process"""
        process = self.processes.get(name)
        if process:
            return process.status.value
        return "not_found"
    
    def get_all_status(self) -> Dict[str, str]:
        """Get status of all processes"""
        return {name: process.status.value for name, process in self.processes.items()}
    
    def get_available_processes(self) -> List[str]:
        """Get list of available process names"""
        return [name for name, process in self.processes.items() if process.is_available()]
    
    def get_process_stats(self) -> Dict[str, Dict[str, Any]]:
        """Get detailed statistics for all processes"""
        stats = {}
        for name, process in self.processes.items():
            stats[name] = {
                "status": process.status.value,
                "is_healthy": process.is_healthy(),
                "is_available": process.is_available(),
                "active_requests": process.get_active_request_count(),
                "restart_count": process.restart_count
            }
        return stats


# Global instance
llm_process_manager = LLMProcessManager()
