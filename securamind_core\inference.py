# securamind_core/inference.py

import logging
import json
import asyncio
from asyncio import Queue
from typing import List, Dict, Any, Optional

from fastapi import APIRouter, HTTPException, Depends
from fastapi.responses import StreamingResponse
from pydantic import BaseModel, Field
# Note: No longer importing Llama directly - using process-based LLMs
from qdrant_client import QdrantClient

from .config import settings
from .translations import get_translation, is_language_supported, DEFAULT_LANGUAGE

# --- Initialize Logging ---
logger = logging.getLogger(__name__) 

# --- Dependencies for process-based LLM access ---
# Note: These functions are kept for compatibility but now use process-based LLMs

def get_qdrant_client() -> QdrantClient:
    from .main import qdrant_client_instance
    if qdrant_client_instance is None:
        logger.error("Qdrant client instance is None in get_qdrant_client.")
        raise HTTPException(status_code=503, detail="Qdrant client not available for RAG.")
    return qdrant_client_instance

# --- Thread-Safe LLM Access Functions ---
async def get_safe_llm_functions():
    """Get thread-safe LLM wrapper functions"""
    from .main import safe_llm_create_chat_completion, safe_embedding_llm_create_embedding
    return safe_llm_create_chat_completion, safe_embedding_llm_create_embedding

# --- Router for Inference Endpoints ---
router = APIRouter(
    prefix="/api/v1/inference",
    tags=["Inference"]
)

# --- Pydantic Models ---
class LLMConfig(BaseModel):
    max_new_tokens: int = Field(settings.max_tokens_default, description="Max new tokens for LLM.")
    temperature: float = Field(settings.temperature_default, description="Temperature for LLM sampling.")
    top_k_retrieval: int = Field(settings.top_k_retrieval_default, description="Number of relevant chunks to retrieve for RAG.")

class InferenceRequest(BaseModel):
    query: str = Field(..., description="The user's query or prompt for the LLM.")
    collection_name: str = Field(..., description="Identifier for the vector collection to use for RAG.")
    system_prompt: str | None = Field(None, description="Optional system prompt to guide the LLM's behavior.")
    chat_history: list[dict[str, str]] = Field([], description="A list of previous user/assistant messages for context.")
    config: LLMConfig = Field(default_factory=LLMConfig, description="Optional LLM generation and RAG parameters.")
    language: str = Field("de", description="Language code for responses (e.g., 'en', 'de', 'fr', 'es'). Defaults to German.")

async def retrieve_relevant_chunks(
    query: str,
    collection_name: str,
    top_k: int,
    actual_qdrant_client: QdrantClient
) -> List[Dict[str, Any]]:
    """
    Why: Find and retrieve text chunks relevant to the user's query from Qdrant.
    How: Embeds the query and performs a similarity search using thread-safe embedding.
    """
    if not query:
        logger.debug("Empty query received for retrieval, returning no chunks.")
        return []
    try:
        # Get thread-safe embedding function
        _, safe_embedding_create = await get_safe_llm_functions()

        # BGE-M3 for dense retrieval does not require specific query/passage prefixes.
        query_embedding_result = await safe_embedding_create([query])
        # Extract embedding from the result (handle both dict and list formats)
        if isinstance(query_embedding_result, dict) and 'data' in query_embedding_result:
            query_vector = query_embedding_result['data'][0]['embedding']
        elif isinstance(query_embedding_result, list) and len(query_embedding_result) > 0:
            # Handle direct list format from embedding model
            query_vector = query_embedding_result[0]
        else:
            raise ValueError(f"Unexpected embedding result format: {type(query_embedding_result)}")

        logger.debug(f"Searching collection '{collection_name}' with top_k={top_k} for query: '{query[:50]}...'")
        search_results = await asyncio.to_thread(
            actual_qdrant_client.search,
            collection_name=collection_name,
            query_vector=query_vector,
            limit=top_k,
        )
        retrieved_docs_payloads = [hit.payload for hit in search_results if hit.payload]
        logger.info(f"Retrieved {len(retrieved_docs_payloads)} chunks from '{collection_name}' for query.")
        return retrieved_docs_payloads
    except Exception as e: # Catch specific Qdrant client exceptions if possible
        logger.error(f"Error during RAG retrieval from '{collection_name}': {e}", exc_info=True)
        return []

def construct_rag_prompt(
    query: str,
    retrieved_chunks: List[Dict[str, Any]],
    chat_history: list[dict[str, str]],
    custom_system_prompt: str | None,
    language: str = DEFAULT_LANGUAGE
) -> list[dict[str, str]]:
    """
    Why: Combine user query, retrieved context, chat history, and system instructions
         into a structured prompt for the LLM with multi-language support.
    How: Formats context and integrates it into the message list using translated strings.
    """

    # Validate and normalize language
    if not is_language_supported(language):
        logger.warning(f"Unsupported language '{language}', falling back to default language '{DEFAULT_LANGUAGE}'")
        language = DEFAULT_LANGUAGE

    # Default system prompt if none provided (translated)
    system_message_content = custom_system_prompt if custom_system_prompt else get_translation(language, "system_prompt")

    # Format the retrieved context (translated)
    context_str = ""
    if retrieved_chunks:
        context_instr = get_translation(language, "context_instructions")
        context_header = get_translation(language, "context_header")
        context_footer = get_translation(language, "context_footer")
        source_label = get_translation(language, "source_label")
        page_label = get_translation(language, "page_label")
        unknown_source = get_translation(language, "unknown_source")

        formatted_chunks = []
        for i, chunk_payload in enumerate(retrieved_chunks):
            text = chunk_payload.get("text", "")
            source = chunk_payload.get("source", unknown_source)
            page = chunk_payload.get("page_number") # May be None
            chunk_info = f"{source_label}: {source}"
            if page is not None: # Check explicitly for None
                chunk_info += f", {page_label}: {page}"
            formatted_chunks.append(f"[{i+1}. {chunk_info}]:\n{text}")
        context_str = context_instr + context_header + "\n\n".join(formatted_chunks) + context_footer
    else:
        context_str = get_translation(language, "no_documents_found")

    # 1. System Prompt (incorporating context instructions)
    messages = [{"role": "system", "content": system_message_content + context_str}]

    # 2. Chat History (if any)
    for entry in chat_history:
        role = entry.get("role")
        content = entry.get("content")
        if isinstance(role, str) and isinstance(content, str):
            messages.append({"role": role, "content": content})
        else:
            logger.warning(f"Skipping invalid chat history entry: {entry}")

    # 3. Current User Query
    messages.append({"role": "user", "content": query})
    return messages

# --- LLM Worker ---
async def _llm_stream_worker_async(
    queue: Queue,
    messages: list[dict[str, str]],
    max_tokens: int,
    temperature: float
):
    """
    Why: Runs the thread-safe llama-cpp stream.
         Puts generated tokens or an EOS marker into an asyncio.Queue.
    How: Uses thread-safe LLM wrapper but processes stream in chunks to minimize lock time.
    """
    try:
        # Get thread-safe LLM function
        safe_llm_create, _ = await get_safe_llm_functions()

        # Create the stream using the thread-safe wrapper
        # All SecuraMind requests are streaming-only
        stream = await safe_llm_create(
            messages=messages,
            max_tokens=max_tokens,
            temperature=temperature
        )

        # Process the stream (async generator from process)
        async for output_chunk in stream:
            if "choices" in output_chunk and output_chunk["choices"]:
                delta = output_chunk["choices"][0].get("delta", {})
                content = delta.get("content")
                if content:
                    queue.put_nowait(content)

        queue.put_nowait(None)

    except Exception as e:
        logger.error(f"Error in LLM stream worker: {e}", exc_info=True)
        queue.put_nowait(e)


async def stream_llm_rag_inference(
    request_data: InferenceRequest,
    actual_qdrant_client: QdrantClient
):
    logger.info(f"Starting RAG inference for query: '{request_data.query[:50]}...' on collection '{request_data.collection_name}'.")

    """
    Why: Perform RAG and stream LLM responses.
    How: Retrieves context, constructs a RAG prompt, then streams LLM generation using thread-safe access.
    """

    # 1. Retrieve relevant chunks TODO: Let a RAG manager model pick a query based on the chat history. Otherwise any follow up request of a user without direct reference will not yield good results. (See GLaDOS project)
    retrieved_chunks = await retrieve_relevant_chunks(
        query=request_data.query,
        collection_name=request_data.collection_name,
        top_k=request_data.config.top_k_retrieval,
        actual_qdrant_client=actual_qdrant_client
    )
    
    # 2. Construct the RAG prompt (with language support)
    messages_for_llm = construct_rag_prompt(
        query=request_data.query,
        retrieved_chunks=retrieved_chunks,
        chat_history=request_data.chat_history,
        custom_system_prompt=request_data.system_prompt,
        language=request_data.language
    )
    
    # 3. Stream generation using the RAG prompt
    if logger.isEnabledFor(logging.DEBUG): # Log full prompt only in debug
        logger.debug(f"Constructed RAG prompt messages: {json.dumps(messages_for_llm, indent=2)}")

    token_queue = asyncio.Queue()

    # Start the async stream worker task
    stream_task = asyncio.create_task(_llm_stream_worker_async(
        token_queue,
        messages_for_llm,
        request_data.config.max_new_tokens,
        request_data.config.temperature
    ))
    try:
        while True:
            item = await token_queue.get()
            if item is None: # EOS marker
                source_payloads = [
                    {
                        "source": chunk.get("source"), 
                        "page": chunk.get("page_number"), # Will be null if not present
                        "text_preview": (chunk.get("text", "")[:100] + "...") if chunk.get("text") else ""
                    } 
                    for chunk in retrieved_chunks # retrieved_chunks is already a list of payloads
                ]
                yield f"data: {json.dumps({'event': 'sources', 'data': source_payloads})}\n\n"
                yield f"data: {json.dumps({'event': 'eos', 'message': 'End of stream'})}\n\n"
                logger.info("RAG stream completed (EOS).")
                break
            elif isinstance(item, Exception):
                logger.error(f"LLM worker signaled an error: {str(item)}", exc_info=item) # Log the exception from worker
                error_message = {"error": f"LLM worker failed: {str(item)}"}
                yield f"data: {json.dumps(error_message)}\n\n"
                break
            elif isinstance(item, str): # A generated token
                yield f"data: {json.dumps({'token': item})}\n\n"
            token_queue.task_done()
    except asyncio.CancelledError:
        logger.warning("Streaming was cancelled by client for RAG inference.")
        raise # Re-raise to allow FastAPI to handle client disconnect
    except Exception as e:
        logger.error(f"Error in RAG streaming logic (queue reading): {e}", exc_info=True)
        error_message = {"error": f"An error occurred during RAG stream processing: {str(e)}"}
        yield f"data: {json.dumps(error_message)}\n\n"
    finally:
        # Cancel the stream task to prevent unconsumed streams from causing GGML_ASSERT failures
        if not stream_task.done():
            logger.debug("Cancelling stream task due to client disconnect or error")
            stream_task.cancel()
            try:
                await stream_task
            except asyncio.CancelledError:
                logger.debug("Stream task cancelled successfully")
            except Exception as e:
                logger.error(f"Error while cancelling stream task: {e}", exc_info=True)

        # Ensure queue is flushed
        while not token_queue.empty():
            await token_queue.get()
            token_queue.task_done()
            logger.debug("Flushed an item from queue during final cleanup.")

# --- Inference API Endpoint ---
@router.post("", response_class=StreamingResponse)
async def handle_rag_inference_request(
    request: InferenceRequest,
    injected_qdrant_client: QdrantClient = Depends(get_qdrant_client)
):
    """
    Why: Provide a RAG-enabled API endpoint for streaming LLM generation with multi-language support.
    How: Retrieves context based on query and collection_name, constructs a RAG prompt,
         and streams back token responses along with source information using thread-safe LLM access.
    """
    if not request.collection_name: # Pydantic should enforce this if not optional
        logger.warning("Inference request received without collection_name.")
        raise HTTPException(status_code=400, detail="collection_name is required for RAG inference.")

    # Validate language parameter
    if not is_language_supported(request.language):
        logger.warning(f"Unsupported language '{request.language}' in request, using default '{DEFAULT_LANGUAGE}'")
        # Note: We don't raise an error here, just log a warning and let the construct_rag_prompt function handle the fallback

    return StreamingResponse(
        stream_llm_rag_inference(
            request_data=request,
            actual_qdrant_client=injected_qdrant_client
        ),
        media_type="text/event-stream"
    )