# securamind_core/ingestion.py

import asyncio
import io
import logging
import uuid
import json # For parsing additional_metadata
import time
import threading
from datetime import datetime, timezone, timedelta
from typing import List, Dict, Any, Optional
from enum import Enum

from fastapi import APIRouter, HTTPException, UploadFile, File, Form, Depends, Path as FastAPIPath
from pydantic import BaseModel, Field
from llama_cpp import Llama
from qdrant_client import QdrantClient, models as qdrant_models
from qdrant_client.http import models as rest_models # For Filter for deletion

from langchain_text_splitters import RecursiveCharacterTextSplitter

import PyPDF2
import docx
import pandas as pd

from .config import settings
from . import utils

# --- Initialize Logging ---

logger = logging.getLogger(__name__)

# --- Ingestion Status Tracking ---

class IngestionStatus(str, Enum):
    """Enumeration of possible ingestion statuses"""
    PENDING = "pending"
    IN_PROGRESS = "in_progress"
    COMPLETED = "completed"
    FAILED = "failed"

class IngestionProgress:
    """Class to track ingestion progress"""
    def __init__(self, ingestion_id: str, collection_name: str, source_name: str, source_identifier: str):
        self.ingestion_id = ingestion_id
        self.collection_name = collection_name
        self.source_name = source_name
        self.source_identifier = source_identifier
        self.status = IngestionStatus.PENDING
        self.total_chunks = 0
        self.processed_chunks = 0
        self.started_at = datetime.now(timezone.utc)
        self.completed_at: Optional[datetime] = None
        self.error_message: Optional[str] = None
        self._lock = threading.Lock()

    def set_total_chunks(self, total: int):
        """Set the total number of chunks to process"""
        with self._lock:
            self.total_chunks = total
            if self.status == IngestionStatus.PENDING:
                self.status = IngestionStatus.IN_PROGRESS

    def increment_processed(self):
        """Increment the number of processed chunks"""
        with self._lock:
            self.processed_chunks += 1

    def mark_completed(self):
        """Mark ingestion as completed"""
        with self._lock:
            self.status = IngestionStatus.COMPLETED
            self.completed_at = datetime.now(timezone.utc)

    def mark_failed(self, error_message: str):
        """Mark ingestion as failed with error message"""
        with self._lock:
            self.status = IngestionStatus.FAILED
            self.error_message = error_message
            self.completed_at = datetime.now(timezone.utc)

    @property
    def percentage(self) -> float:
        """Calculate completion percentage"""
        with self._lock:
            if self.total_chunks == 0:
                return 0.0
            return (self.processed_chunks / self.total_chunks) * 100.0

    @property
    def estimated_time_remaining(self) -> Optional[float]:
        """Estimate remaining time in seconds"""
        with self._lock:
            if self.status != IngestionStatus.IN_PROGRESS or self.processed_chunks == 0:
                return None

            elapsed_time = (datetime.now(timezone.utc) - self.started_at).total_seconds()
            chunks_remaining = self.total_chunks - self.processed_chunks

            if chunks_remaining <= 0:
                return 0.0

            time_per_chunk = elapsed_time / self.processed_chunks
            return time_per_chunk * chunks_remaining

# Global progress tracking storage
_ingestion_progress: Dict[str, IngestionProgress] = {}
_progress_lock = threading.Lock()

def create_ingestion_progress(collection_name: str, source_name: str, source_identifier: str) -> str:
    """Create a new ingestion progress tracker and return the ingestion_id"""
    ingestion_id = str(uuid.uuid4())
    progress = IngestionProgress(ingestion_id, collection_name, source_name, source_identifier)

    with _progress_lock:
        _ingestion_progress[ingestion_id] = progress

    logger.info(f"Created ingestion progress tracker: {ingestion_id} for source '{source_name}'")
    return ingestion_id

def get_ingestion_progress(ingestion_id: str) -> Optional[IngestionProgress]:
    """Get ingestion progress by ID"""
    with _progress_lock:
        return _ingestion_progress.get(ingestion_id)

def cleanup_old_progress():
    """Clean up progress records older than 1 hour"""
    cutoff_time = datetime.now(timezone.utc) - timedelta(hours=1)

    with _progress_lock:
        to_remove = []
        for ingestion_id, progress in _ingestion_progress.items():
            if progress.completed_at and progress.completed_at < cutoff_time:
                to_remove.append(ingestion_id)

        for ingestion_id in to_remove:
            del _ingestion_progress[ingestion_id]
            logger.debug(f"Cleaned up old ingestion progress: {ingestion_id}")

# Schedule cleanup every hour
import asyncio
from datetime import timedelta

async def periodic_cleanup():
    """Periodic cleanup task"""
    while True:
        await asyncio.sleep(3600)  # 1 hour
        cleanup_old_progress()

# --- Background Processing Functions ---
async def process_text_ingestion_background(
    ingestion_id: str,
    collection_name: str,
    source_identifier: str,
    source_name: str,
    text_content: str,
    metadata: Optional[Dict[str, Any]] = None
):
    """Process text ingestion in the background"""
    progress = get_ingestion_progress(ingestion_id)
    if not progress:
        logger.error(f"Progress tracker not found for ingestion {ingestion_id}")
        return

    try:
        # Get Qdrant client
        from .main import qdrant_client_instance
        if not qdrant_client_instance:
            raise RuntimeError("Qdrant client not available")

        # Chunk the text
        text_chunks = chunk_text(text_content)
        if not text_chunks:
            progress.mark_failed("No text chunks generated from provided text")
            return

        # Prepare items for embedding
        items_to_embed = []
        base_payload_fields = {
            "source": source_name,
            "source_identifier": source_identifier,
        }

        # Add user-provided metadata
        if metadata:
            for k, v in metadata.items():
                if k not in base_payload_fields:
                    base_payload_fields[k] = v

        for chunk_str in text_chunks:
            chunk_metadata_payload = base_payload_fields.copy()
            items_to_embed.append({"text_content": chunk_str, "metadata_payload": chunk_metadata_payload})

        # Process embedding and storage
        chunks_embedded = await embed_and_store_chunks(
            collection_name, items_to_embed, qdrant_client_instance, progress
        )

        # Mark as completed
        progress.mark_completed()
        logger.info(f"Background text ingestion complete for '{source_name}' (ID: {ingestion_id}). Embedded {chunks_embedded} chunks.")

    except Exception as e:
        progress.mark_failed(str(e))
        logger.error(f"Background text ingestion failed for '{source_name}' (ID: {ingestion_id}): {e}", exc_info=True)

async def process_file_ingestion_background(
    ingestion_id: str,
    collection_name: str,
    source_identifier: str,
    source_name: str,
    file_content: bytes,
    original_filename: str,
    content_type: str,
    additional_metadata: Optional[str] = None
):
    """Process file ingestion in the background"""
    progress = get_ingestion_progress(ingestion_id)
    if not progress:
        logger.error(f"Progress tracker not found for ingestion {ingestion_id}")
        return

    try:
        # Get Qdrant client
        from .main import qdrant_client_instance
        if not qdrant_client_instance:
            raise RuntimeError("Qdrant client not available")

        # Parse file and prepare items (same logic as before)
        items_to_embed = []
        base_payload_fields = {
            "source": source_name,
            "source_identifier": source_identifier,
            "original_filename": original_filename
        }

        # Add additional metadata
        if additional_metadata:
            try:
                parsed_add_meta = json.loads(additional_metadata)
                if isinstance(parsed_add_meta, dict):
                    for k, v in parsed_add_meta.items():
                        if k not in base_payload_fields:
                            base_payload_fields[k] = v
            except json.JSONDecodeError:
                logger.warning(f"Failed to parse additional_metadata for ingestion {ingestion_id}")

        # Parse file content based on type
        if content_type == "application/pdf" or original_filename.lower().endswith(".pdf"):
            pages_data = parse_pdf(file_content)
            if not pages_data:
                progress.mark_failed(f"No text content extracted from PDF: {original_filename}")
                return

            for page_info in pages_data:
                page_chunks = chunk_text(page_info["text"])
                for chunk_str in page_chunks:
                    chunk_metadata_payload = base_payload_fields.copy()
                    chunk_metadata_payload["page_number"] = page_info["page_number"]
                    items_to_embed.append({"text_content": chunk_str, "metadata_payload": chunk_metadata_payload})

        else:
            # Handle other file types (DOCX, TXT, CSV)
            raw_text = ""
            if content_type in ["application/vnd.openxmlformats-officedocument.wordprocessingml.document", "application/msword"] or original_filename.lower().endswith(".docx"):
                raw_text = parse_docx(file_content)
            elif content_type == "text/plain" or original_filename.lower().endswith(".txt"):
                raw_text = parse_txt(file_content)
            elif content_type == "text/csv" or original_filename.lower().endswith(".csv"):
                raw_text = parse_csv(file_content)
            else:
                progress.mark_failed(f"Unsupported file type: {content_type}")
                return

            if not raw_text.strip():
                progress.mark_failed(f"No text content extracted from file: {original_filename}")
                return

            text_chunks = chunk_text(raw_text)
            for chunk_str in text_chunks:
                chunk_metadata_payload = base_payload_fields.copy()
                items_to_embed.append({"text_content": chunk_str, "metadata_payload": chunk_metadata_payload})

        if not items_to_embed:
            progress.set_total_chunks(0)
            progress.mark_completed()
            logger.info(f"Background file ingestion complete for '{original_filename}' (ID: {ingestion_id}). No content to embed.")
            return

        # Process embedding and storage
        chunks_embedded = await embed_and_store_chunks(
            collection_name, items_to_embed, qdrant_client_instance, progress
        )

        # Mark as completed
        progress.mark_completed()
        logger.info(f"Background file ingestion complete for '{original_filename}' (ID: {ingestion_id}). Embedded {chunks_embedded} chunks.")

    except Exception as e:
        progress.mark_failed(str(e))
        logger.error(f"Background file ingestion failed for '{original_filename}' (ID: {ingestion_id}): {e}", exc_info=True)



def get_embedding_llm() -> Llama:
    from .main import embedding_llm_instance
    if embedding_llm_instance is None:
        logger.error("Embedding LLM instance is None in get_embedding_llm.")
        raise HTTPException(status_code=503, detail="Embedding LLM not available.")
    return embedding_llm_instance

def get_qdrant_client() -> QdrantClient:
    from .main import qdrant_client_instance
    if qdrant_client_instance is None:
        logger.error("Qdrant client instance is None in get_qdrant_client.")
        raise HTTPException(status_code=503, detail="Qdrant client not available.")
    return qdrant_client_instance


# --- Router for Ingestion Endpoints ---
router = APIRouter(
    prefix="/api/v1/ingest",
    tags=["Ingestion"]
)

# Add status router
status_router = APIRouter(
    prefix="/api/v1/ingestion",
    tags=["Ingestion Status"]
)

# --- Pydantic Models for Ingestion API ---
class IngestionResponse(BaseModel):
    ingestion_id: str
    collection_name: str
    source_identifier: str
    source_name: str
    status: str = "accepted"
    message: str = "Ingestion started. Use the ingestion_id to track progress."

class IngestionCompletedResponse(BaseModel):
    ingestion_id: str
    collection_name: str
    source_identifier: str
    source_name: str
    documents_processed: int
    total_chunks_embedded: int
    status: str = "success"

class IngestionProgressResponse(BaseModel):
    ingestion_id: str
    status: IngestionStatus
    progress: Dict[str, Any]
    metadata: Dict[str, Any]
    estimated_time_remaining: Optional[float] = None

class IngestionStatusProgress(BaseModel):
    total_chunks: int
    processed_chunks: int
    percentage: float

class IngestionStatusMetadata(BaseModel):
    collection_name: str
    source_name: str
    source_identifier: str
    started_at: str
    completed_at: Optional[str] = None
    error_message: Optional[str] = None

class TextIngestionRequest(BaseModel):
    collection_name: str = Field(..., description="Name of the Qdrant collection to use/create.")
    source_identifier: str = Field(..., description="Unique identifier for this source, used for deletion.")
    source_name: str = Field(..., description="User-defined name for this source, stored in metadata as 'source'.")
    text_content: str = Field(..., description="Raw text content to ingest.")
    metadata: Optional[Dict[str, Any]] = Field(None, description="Optional additional metadata (e.g., category). This will be merged into the point payload.")

class DeletionResponse(BaseModel):
    collection_name: str
    source_identifier: str
    status: str
    message: str | None = None

# --- Status Endpoint ---
@status_router.get("/status/{ingestion_id}", response_model=IngestionProgressResponse)
async def get_ingestion_status(ingestion_id: str = FastAPIPath(..., description="The ingestion ID to check status for")):
    """
    Get the status and progress of an ongoing or completed ingestion operation.

    Returns detailed information about the ingestion including:
    - Current status (pending, in_progress, completed, failed)
    - Progress information (total chunks, processed chunks, percentage)
    - Metadata (collection name, source info, timestamps)
    - Estimated time remaining (for in-progress operations)
    """
    progress = get_ingestion_progress(ingestion_id)

    if not progress:
        raise HTTPException(
            status_code=404,
            detail=f"Ingestion ID '{ingestion_id}' not found. It may have expired or never existed."
        )

    # Build response
    progress_data = {
        "total_chunks": progress.total_chunks,
        "processed_chunks": progress.processed_chunks,
        "percentage": progress.percentage
    }

    metadata = {
        "collection_name": progress.collection_name,
        "source_name": progress.source_name,
        "source_identifier": progress.source_identifier,
        "started_at": progress.started_at.isoformat(),
        "completed_at": progress.completed_at.isoformat() if progress.completed_at else None,
        "error_message": progress.error_message
    }

    return IngestionProgressResponse(
        ingestion_id=ingestion_id,
        status=progress.status,
        progress=progress_data,
        metadata=metadata,
        estimated_time_remaining=progress.estimated_time_remaining
    )

# --- File Parsing Functions ---
def parse_pdf(file_content: bytes) -> List[Dict[str, Any]]:
    """
    Why: Extract text from a PDF file, page by page.
    Returns: A list of dictionaries, each with "text" and "page_number" (1-indexed).
    """
    pages_data = []
    try:
        reader = PyPDF2.PdfReader(io.BytesIO(file_content))
        for page_num, page in enumerate(reader.pages):
            page_text = page.extract_text()
            if page_text and page_text.strip():
                pages_data.append({"text": page_text, "page_number": page_num + 1}) # 1-indexed page number
            elif page_text is not None: # Text was extracted but is whitespace only
                 logger.debug(f"PDF page {page_num + 1} contained only whitespace, skipping.")
            # If page_text is None, it means extraction failed for the page, PyPDF2 usually handles this gracefully.
        if not pages_data:
            logger.warning("No text content extracted from any page of the PDF.")
        return pages_data
    except Exception as e:
        logger.error(f"Error parsing PDF: {e}", exc_info=True)
        raise HTTPException(status_code=400, detail=f"Could not parse PDF: {str(e)}")

def parse_docx(file_content: bytes) -> str:
    """
    Why: Extract text from a DOCX file.
    Note: Page numbers are not extracted for DOCX as pagination is dynamic and determined by
          the rendering application (e.g., MS Word). True page number association per paragraph
          is complex and unreliable without rendering the document.
    """
    try:
        doc = docx.Document(io.BytesIO(file_content))
        text = "\n".join([paragraph.text for paragraph in doc.paragraphs])
        return text
    except Exception as e:
        logger.error(f"Error parsing DOCX: {e}", exc_info=True)
        raise HTTPException(status_code=400, detail=f"Could not parse DOCX: {str(e)}")

def parse_txt(file_content: bytes) -> str:
    """Why: Extract text from a TXT file (assuming UTF-8)."""
    try:
        return file_content.decode('utf-8')
    except UnicodeDecodeError:
        logger.warning("Failed to decode TXT as UTF-8, trying latin-1.")
        try:
            return file_content.decode('latin-1')
        except Exception as e:
            logger.error(f"Error parsing TXT, failed UTF-8 and latin-1: {e}", exc_info=True)
            raise HTTPException(status_code=400, detail=f"Could not parse TXT: {str(e)}")

def parse_csv(file_content: bytes) -> str:
    """
    Why: Extract text from a CSV file.
    How: Concatenates all string cells row by row.
    """
    try:
        df = pd.read_csv(io.BytesIO(file_content))
        csv_text_parts = []
        for index, row in df.iterrows():
            row_text = ", ".join([f"{col}: {str(item)}" for col, item in row.dropna().items()])
            csv_text_parts.append(f"Row {index + 1}: {row_text}")
        return "\n".join(csv_text_parts)
    except Exception as e:
        logger.error(f"Error parsing CSV: {e}", exc_info=True)
        raise HTTPException(status_code=400, detail=f"Could not parse CSV: {str(e)}")

def chunk_text(text: str, chunk_size: int = 1000, chunk_overlap: int = 200) -> List[str]:
    """Why: Split large text into smaller, manageable chunks for embedding."""
    if not text or not text.strip():
        logger.warning("Attempted to chunk empty or whitespace-only text.")
        return []
    text_splitter = RecursiveCharacterTextSplitter(
        chunk_size=chunk_size,
        chunk_overlap=chunk_overlap,
        length_function=len,
        is_separator_regex=False,
    )
    return text_splitter.split_text(text)

# --- Embedding Generation and Storage Logic ---
async def embed_and_store_chunks(
    collection_name: str,
    items_to_embed: List[Dict[str, Any]], # Each dict: {"text_content": str, "metadata_payload": Dict}
    qdrant_client: QdrantClient,
    progress: Optional[IngestionProgress] = None
) -> int:
    """
    Why: Generate embeddings for text chunks and store them in Qdrant.
    How: Uses thread-safe embedding LLM access and Qdrant client.
         Ensures the collection exists.
         The `metadata_payload` for each item is used directly as the Qdrant point's payload.
         Updates progress tracking if provided.
    Returns: Number of successfully embedded chunks.
    """
    if not utils.get_or_create_collection(qdrant_client, collection_name, settings.vector_dimension):
        logger.error(f"Failed to ensure collection '{collection_name}' for embedding.")
        raise HTTPException(status_code=500, detail=f"Could not ensure collection '{collection_name}' exists.")

    # Set total chunks for progress tracking
    if progress:
        progress.set_total_chunks(len(items_to_embed))

    # Get thread-safe embedding function
    from .main import safe_embedding_llm_create_embedding

    points_to_upsert: List[qdrant_models.PointStruct] = []
    processed_chunks_count = 0

    for item in items_to_embed:
        chunk_text_content = item["text_content"]
        metadata_for_payload = item["metadata_payload"]

        if not chunk_text_content.strip():
            log_ref = metadata_for_payload.get('source', metadata_for_payload.get('source_identifier', 'unknown item'))
            logger.debug(f"Skipping empty chunk for '{log_ref}'.")
            continue
        try:
            # BGE-M3 for dense retrieval does not require specific query/passage prefixes.
            embedding_result = await safe_embedding_llm_create_embedding([chunk_text_content])
            vector = embedding_result['data'][0]['embedding']
            
            # Construct payload: text itself + all provided metadata
            # metadata_for_payload is assumed to be the final desired payload dict (excluding 'text')
            payload = {"text": chunk_text_content, **metadata_for_payload}
            
            points_to_upsert.append(qdrant_models.PointStruct(
                id=str(uuid.uuid4()),
                vector=vector,
                payload=payload
            ))
            processed_chunks_count += 1

            # Update progress tracking
            if progress:
                progress.increment_processed()
        except Exception as e:
            log_ref = metadata_for_payload.get('source', metadata_for_payload.get('source_identifier', 'unknown item'))
            logger.error(f"Error embedding chunk for '{log_ref}': {e}", exc_info=True)

    if points_to_upsert:
        try:
            await asyncio.to_thread(
                qdrant_client.upsert,
                collection_name=collection_name,
                points=points_to_upsert,
                wait=True # Consider making wait configurable or False for very large batches
            )
            log_ref_summary = metadata_for_payload.get('source', metadata_for_payload.get('source_identifier', 'multiple sources')) if items_to_embed else "N/A"
            logger.info(f"Successfully upserted {len(points_to_upsert)} points to '{collection_name}' (related to source '{log_ref_summary}').")
            return len(points_to_upsert)
        except Exception as e:
            log_ref_summary = metadata_for_payload.get('source', metadata_for_payload.get('source_identifier', 'multiple sources')) if items_to_embed else "N/A"
            logger.error(f"Error upserting points to Qdrant collection '{collection_name}' (related to '{log_ref_summary}'): {e}", exc_info=True)
            raise HTTPException(status_code=500, detail=f"Qdrant upsert failed for {collection_name}.")
    elif items_to_embed and processed_chunks_count == 0:
        log_ref_summary = metadata_for_payload.get('source', metadata_for_payload.get('source_identifier', 'multiple sources')) if items_to_embed else "N/A"
        logger.warning(f"No valid points generated for upsertion from {len(items_to_embed)} initial items (related to '{log_ref_summary}').")
    return 0

# --- Ingestion API Endpoints ---
@router.post("/file", response_model=IngestionResponse)
async def ingest_file_endpoint(
    collection_name: str = Form(..., description="Name of the Qdrant collection to use/create."),
    source_identifier: str = Form(..., description="Unique identifier for this source, used for deletion."),
    source_name: str = Form(..., description="User-defined name for this source, stored in metadata as 'source'."),
    file: UploadFile = File(..., description="File to ingest (PDF, DOCX, TXT, CSV)."),
    additional_metadata: Optional[str] = Form(None, description="Optional: JSON string of key-value pairs for additional metadata.")
):
    """
    Ingest a file asynchronously.
    Returns immediately with ingestion_id for progress tracking.
    """
    # Create progress tracking
    ingestion_id = create_ingestion_progress(collection_name, source_name, source_identifier)

    content_type = file.content_type
    original_filename = file.filename or "uploaded_file"
    logger.info(f"Starting async file ingestion for '{original_filename}' (Source Name: '{source_name}', ID: '{source_identifier}') into collection '{collection_name}'. Type: {content_type}. Ingestion ID: {ingestion_id}")

    # Read file content
    file_content = await file.read()
    await file.close()

    # Start background processing
    asyncio.create_task(process_file_ingestion_background(
        ingestion_id=ingestion_id,
        collection_name=collection_name,
        source_identifier=source_identifier,
        source_name=source_name,
        file_content=file_content,
        original_filename=original_filename,
        content_type=content_type,
        additional_metadata=additional_metadata
    ))

    # Return immediately with ingestion_id
    return IngestionResponse(
        ingestion_id=ingestion_id,
        collection_name=collection_name,
        source_identifier=source_identifier,
        source_name=source_name,
        status="accepted",
        message="File ingestion started. Use the ingestion_id to track progress."
    )



@router.post("/text", response_model=IngestionResponse)
async def ingest_text_endpoint(
    request: TextIngestionRequest
):
    """
    Ingest raw text content asynchronously.
    Returns immediately with ingestion_id for progress tracking.
    """
    # Validate input
    if not request.text_content.strip():
        raise HTTPException(status_code=400, detail="Text content cannot be empty.")

    # Create progress tracking
    ingestion_id = create_ingestion_progress(request.collection_name, request.source_name, request.source_identifier)

    logger.info(f"Starting async text ingestion for source '{request.source_name}' (ID: '{request.source_identifier}') into collection '{request.collection_name}'. Ingestion ID: {ingestion_id}")

    # Start background processing
    asyncio.create_task(process_text_ingestion_background(
        ingestion_id=ingestion_id,
        collection_name=request.collection_name,
        source_identifier=request.source_identifier,
        source_name=request.source_name,
        text_content=request.text_content,
        metadata=request.metadata
    ))

    # Return immediately with ingestion_id
    return IngestionResponse(
        ingestion_id=ingestion_id,
        collection_name=request.collection_name,
        source_identifier=request.source_identifier,
        source_name=request.source_name,
        status="accepted",
        message="Text ingestion started. Use the ingestion_id to track progress."
    )

@router.delete("/source/{collection_name}/{source_identifier:path}", response_model=DeletionResponse)
async def delete_source_from_collection_endpoint(
    collection_name: str = FastAPIPath(..., description="The name of the Qdrant collection."),
    source_identifier: str = FastAPIPath(..., description="The unique identifier of the source to delete. URL encode if needed."),
    qdrant: QdrantClient = Depends(get_qdrant_client)
):
    logger.info(f"Attempting to delete source with ID '{source_identifier}' from collection '{collection_name}'.")
    if not collection_name or not source_identifier: # Should be caught by FastAPI Path validation
        raise HTTPException(status_code=400, detail="collection_name and source_identifier are required path parameters.")

    try:
        collections_response = await asyncio.to_thread(qdrant.get_collections)
        collection_exists = any(col.name == collection_name for col in collections_response.collections)
        
        if not collection_exists:
            logger.warning(f"Collection '{collection_name}' not found. Nothing to delete for source ID '{source_identifier}'.")
            return DeletionResponse(
                collection_name=collection_name,
                source_identifier=source_identifier,
                status="not_found",
                message=f"Collection '{collection_name}' does not exist."
            )
            
        points_filter = rest_models.Filter(
            must=[
                rest_models.FieldCondition(
                    key="source_identifier",
                    match=rest_models.MatchValue(value=source_identifier)
                )
            ]
        )


        operation_result = await asyncio.to_thread(
            qdrant.delete,
            collection_name=collection_name,
            points_selector=rest_models.FilterSelector(filter=points_filter),
            wait=True
        )
        
        op_status_str = str(operation_result.status if operation_result else "Unknown").lower()
        logger.info(f"Delete operation for source ID '{source_identifier}' in collection '{collection_name}' completed with status: {op_status_str}")

        if op_status_str == "completed" or op_status_str == "acknowledged":

            return DeletionResponse(
                collection_name=collection_name,
                source_identifier=source_identifier,
                status="success" if op_status_str == "completed" else "acknowledged",
                message=f"Data points for source ID '{source_identifier}' from '{collection_name}' processed for deletion. Status: {op_status_str}"
            )
        else:
            logger.error(f"Deletion for source ID '{source_identifier}' in '{collection_name}' resulted in unexpected Qdrant status: {op_status_str}")
            return DeletionResponse(
                collection_name=collection_name,
                source_identifier=source_identifier,
                status="failed",
                message=f"Deletion for source ID '{source_identifier}' in '{collection_name}' resulted in Qdrant status: {op_status_str}"
            )

    except HTTPException: # Re-raise HTTPExceptions that might occur (e.g. from get_qdrant_client)
        raise
    except Exception as e:
        logger.error(f"Error deleting source ID '{source_identifier}' from collection '{collection_name}': {e}", exc_info=True)
        raise HTTPException(status_code=500, detail=f"Failed to delete source ID '{source_identifier}': {str(e)}")